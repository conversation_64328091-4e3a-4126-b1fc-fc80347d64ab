/* Stili per il documento stats
 *
 * Gestisce l'aspetto delle statistiche utente, inclusi i conteggi,
 * i grafici e la presentazione delle analisi recenti.
 */

/* Spinner inline per i bottoni durante le operazioni di disconnessione */
.spinner-inline {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
    margin-right: 5px;
    vertical-align: middle;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Stile specifico per il pulsante di logout */
.stats-logout-link {
    position: relative;
    transition: all 0.3s ease;
    display: inline-block;
    background-color: #f1f1f1;
    border: 1px solid #ddd;
    color: #555;
    padding: 3px 8px;
    border-radius: 3px;
    text-decoration: none;
    font-size: 11px;
}

.stats-logout-link:hover {
    background-color: #e9e9e9;
    text-decoration: none;
    color: #333;
}

/* Stato di logout in corso */
.stats-logout-link.logging-out {
    pointer-events: none;
    opacity: 0.7;
    background-color: #eaeaea;
}

/* Spesa stimata - stile base */
#cost-estimate,
#estimated-cost {
    display: inline-block !important; /* Forza la visualizzazione */
    min-width: 50px; /* Larghezza minima per evitare sfarfallii */
    text-align: right; /* Allineamento a destra per i numeri */
    font-family: monospace; /* Font a larghezza fissa per allineamento */
    font-weight: bold; /* Rende il testo più visibile */
    color: #333; /* Colore scuro per migliore leggibilità */
    transition: color 0.3s ease, transform 0.3s ease;
    opacity: 1 !important; /* Forza l'opacità */
    visibility: visible !important; /* Forza la visibilità */
}

/* Classe per nascondere elementi */
.hidden {
    display: none !important;
}

/* Layout principale delle statistiche - allineato con subscriber management */
/* NOTA: Questi stili si applicano SOLO quando .stats-column NON è dentro .document-viewer-widget */
.stats-column:not(.document-viewer-widget .stats-column) {
    display: block; /* Esplicito per evitare problemi di visualizzazione */
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.08);
    padding: 20px;
    flex: 0 0 300px;
    max-width: 300px;
    box-sizing: border-box;
    margin-right: 20px;
    border: 1px solid #e1e1e1;
    overflow: visible !important; /* Assicura che i tooltip non vengano tagliati */
    position: relative;
}

/* Stili specifici per .stats-column dentro il document viewer widget */
.document-viewer-widget .stats-column {
    /* Questi stili sono già definiti in document-viewer.css con alta specificità */
    /* Non sovrascrivere qui per evitare conflitti */
    overflow: visible !important; /* Mantieni solo questo per i tooltip */
}

/* Sezione delle statistiche - allineata con subscriber management */
.stats-section {
    background-color: #ffffff;
    border-radius: 8px;
    border: 1px solid #e1e1e1;
    margin-bottom: 20px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stats-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.stats-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #e1e1e1;
    border-radius: 8px 8px 0 0;
    cursor: pointer;
    margin-bottom: 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: #333;
}

.stats-section-content {
    padding: 20px;
    background: #fff;
    border-radius: 0 0 8px 8px;
    transition: max-height 0.3s;
    position: relative;
    z-index: 1;
    overflow: visible !important;
}

/* HIGHEST SPECIFICITY for Document Stats Grid - Layout a due colonne per analisi-token e spese */
#user-stats-container .stats-section-content .stats-grid,
#user-stats-container .stats-grid,
.stats-section-content .stats-grid {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important; /* Due colonne per analisi-token e spese */
    gap: 10px !important;
    margin-bottom: 15px !important;
    width: 100% !important;
    max-width: 100% !important;
    overflow: visible !important;
}

/* Layout specifico per la riga del credito - una sola colonna centrata */
#user-stats-container .credit-row,
.stats-section-content .credit-row {
    display: grid !important;
    grid-template-columns: 1fr !important; /* Una sola colonna per il credito */
    grid-column: 1 / -1 !important; /* Occupa tutta la larghezza */
    margin-top: 10px !important;
}

/* Layout a righe per le statistiche - localizzato */
#user-stats-container .stats-row,
.stats-section-content .stats-row {
    display: contents !important;
    width: 100% !important;
    overflow: visible !important;
}

/* Layout specifico per le righe usage e costs - due colonne */
#user-stats-container .usage-row,
#user-stats-container .costs-row,
.stats-section-content .usage-row,
.stats-section-content .costs-row {
    width: 100% !important;
    display: contents !important;
}

/* Layout specifico per la riga credit - singola colonna centrata */
#user-stats-container .credit-row,
.stats-section-content .credit-row {
    width: 100% !important;
    display: grid !important;
    grid-template-columns: 1fr !important;
    grid-column: 1 / -1 !important;
    margin-top: 10px !important;
}

/* Nascondere la riga total-cost */
#user-stats-container .total-cost-row,
.stats-section-content .total-cost-row {
    display: none !important;
}

/* Colori personalizzati per ciascuna tipologia di costo - localizzati */
#user-stats-container .costs-row .stats-item,
.stats-section-content .costs-row .stats-item {
    background: linear-gradient(135deg, #fff8e1 0%, #fff3c4 100%);
    border: 1px solid #ffcc02;
    box-shadow: 0 2px 4px rgba(255, 204, 2, 0.1);
}

#user-stats-container .total-cost-row .stats-item,
.stats-section-content .total-cost-row .stats-item {
    background: linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%);
    border: 1px solid #e17055;
    box-shadow: 0 2px 4px rgba(225, 112, 85, 0.1);
}

#user-stats-container .credit-row .stats-item,
.stats-section-content .credit-row .stats-item {
    background: linear-gradient(135deg, #d1f2eb 0%, #a3e4d7 100%);
    border: 1px solid #00b894;
    box-shadow: 0 2px 4px rgba(0, 184, 148, 0.1);
    padding: 10px;
}

/* HIGHEST SPECIFICITY for Document Stats Items - Prevents all conflicts */
#user-stats-container .stats-grid .stats-item,
#user-stats-container .stats-section-content .stats-item,
.stats-section-content .stats-grid .stats-item,
.stats-section-content .stats-item {
    overflow: visible !important;
    position: relative !important;
    background: #fff !important;
    border-radius: 8px !important;
    padding: 12px !important;
    border: 1px solid #e1e1e1 !important;
    transition: transform 0.2s ease, box-shadow 0.2s ease !important;
    text-align: center !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05) !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    min-height: 90px !important;
}

#user-stats-container .stats-grid .stats-item:hover,
#user-stats-container .stats-section-content .stats-item:hover,
.stats-section-content .stats-grid .stats-item:hover,
.stats-section-content .stats-item:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
}

/* HIGHEST SPECIFICITY for Document Stats Labels - Prevents all conflicts */
#user-stats-container .stats-grid .stats-label,
#user-stats-container .stats-section-content .stats-label,
.stats-section-content .stats-grid .stats-label,
.stats-section-content .stats-label {
    font-size: 0.7rem !important;
    color: #666 !important;
    margin-bottom: 8px !important;
    position: relative !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-weight: 500 !important;
    overflow: visible !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    order: 2 !important;
}

#user-stats-container .stats-label .stats-tooltip,
.stats-section-content .stats-label .stats-tooltip {
    left: 50%;
}

/* CORREZIONE: miglioramento dell'icona info e del tooltip - MINIMALE */
.stats-info-icon {
    position: relative;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: #f0f0f0;
    color: #999;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    font-style: italic;
    margin-left: 4px;
    cursor: help;
    border: none;
    transition: all 0.2s ease;
}

.stats-info-icon:hover {
    background: #e0e0e0;
}

/* CORREZIONE: tooltip con posizionamento absolute ma con z-index elevato */
.stats-tooltip {
    position: absolute;
    background: rgba(51, 51, 51, 0.95);
    color: #fff;
    padding: 6px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: normal;
    width: 180px;
    z-index: 9999;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    transform: translateX(-50%);
    bottom: 130%;
    left: 50%;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
    pointer-events: none;
    margin: 0 auto;
    text-align: center;
    max-width: calc(100vw - 40px);
    word-break: normal;
    word-wrap: break-word;
}

/* CORREZIONE: freccia del tooltip - posizionata SOTTO il tooltip puntando verso l'icona */
.stats-tooltip:after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -4px;
    border: 4px solid transparent;
    border-top-color: rgba(51, 51, 51, 0.95);
}

/* HIGHEST SPECIFICITY for Document Stats - Prevents all conflicts */
#user-stats-container .stats-grid .stats-value,
#user-stats-container .stats-section-content .stats-value,
.stats-section-content .stats-grid .stats-value,
.stats-section-content .stats-value {
    font-size: 1.4rem !important;
    font-weight: 700 !important;
    color: #333 !important;
    text-align: center !important;
    margin-bottom: 4px !important;
    order: 1 !important;
}

/* Colori personalizzati per valori */
.cost-highlight {
    color: #e67e22;
    font-size: 12px;
}

.total-cost-highlight {
    color: #d35400;
    font-weight: 700;
    font-size: 12px;
}

.credit-highlight {
    color: #00b894;
    font-size: 14px;
    font-weight: 700;
}

/* Effetto di aggiornamento per i valori */
.highlight-update {
    animation: highlight-update 2s ease-out 1;
    font-weight: bold;
}

@keyframes highlight-update {
    0% { background-color: rgba(255, 222, 173, 0.9); color: #d35400; }
    50% { background-color: rgba(255, 222, 173, 0.6); }
    100% { background-color: transparent; color: inherit; }
}

/* Date info - RIMOSSA */

/* Pulsante aggiornamento - MINIMALE */
.refresh-stats-btn {
    background: #f5f5f5;
    border: none;
    padding: 5px 8px;
    border-radius: 3px;
    cursor: pointer;
    margin-top: 6px;
    font-size: 12px;
    display: block;
    width: 100%;
    color: #555;
    text-align: center;
    transition: all 0.2s ease;
}

.refresh-stats-btn:hover {
    background: #e0e0e0;
}

/* Icona toggle - MINIMALE */
.toggle-icon {
    width: 12px;
    height: 12px;
    position: relative;
    transition: transform 0.3s;
}

.toggle-icon:before, .toggle-icon:after {
    content: '';
    position: absolute;
    background-color: #777;
    transition: transform 0.3s;
}

.toggle-icon:before {
    width: 2px;
    height: 12px;
    top: 0;
    left: 5px;
}

.toggle-icon:after {
    width: 12px;
    height: 2px;
    top: 5px;
    left: 0;
}

.toggle-icon.expanded:after {
    opacity: 0;
    transform: rotate(90deg);
}

.toggle-icon.collapsed:before {
    transform: rotate(90deg);
}

/* Analisi recenti - MINIMALE */
.recent-analyses-list {
    list-style: none;
    padding: 0;
    margin: 0;
    margin-top: 6px;
}

.recent-analysis-item {
    padding: 6px;
    border-bottom: 1px solid #f0f0f0;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.recent-analysis-item:last-child {
    border-bottom: none;
}

.recent-analysis-item:hover {
    background-color: #f5f5f5;
}

.analysis-item-title {
    font-weight: 500;
    margin-bottom: 2px;
    color: #333;
}

.analysis-item-meta {
    display: flex;
    justify-content: space-between;
    font-size: 10px;
    color: #777;
}

.no-analyses {
    font-style: italic;
    color: #999;
    text-align: center;
    padding: 10px 0;
    font-size: 12px;
}

/* Stati di loading e errore - MINIMALE */
.stats-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px;
    color: #777;
    flex-direction: column;
    font-size: 12px;
}

.stats-spinner {
    border: 2px solid #f5f5f5;
    border-radius: 50%;
    border-top: 2px solid #999;
    width: 16px;
    height: 16px;
    animation: spin 1s linear infinite;
    margin-bottom: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.stats-error {
    color: #e74c3c;
    padding: 10px;
    text-align: center;
    background-color: #fef5f5;
    border-radius: 3px;
    font-size: 12px;
}

#retry-stats-btn {
    margin-top: 6px;
    padding: 4px 8px;
    background-color: #f5f5f5;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 11px;
}

#retry-stats-btn:hover {
    background-color: #e6e6e6;
}

/* Responsività */
@media (max-width: 768px) {
    .stats-row {
        flex-direction: column;
    }

    .stats-tooltip {
        width: 160px;
        left: 50%;
        transform: translateX(-50%);
        top: auto;
        bottom: 130%;
        max-width: calc(100vw - 20px);
    }
}

/* Ottimizzazione per schermi molto piccoli */
@media (max-width: 600px) {
    #user-stats-container .stats-section-content .stats-grid,
    #user-stats-container .stats-grid,
    .stats-section-content .stats-grid {
        grid-template-columns: 1fr !important; /* Una colonna su schermi piccoli */
        gap: 8px !important;
    }

    /* Su schermi piccoli, anche il credito diventa una normale card */
    #user-stats-container .credit-row,
    .stats-section-content .credit-row {
        display: contents !important;
        grid-column: auto !important;
        margin-top: 0 !important;
    }

    #user-stats-container .stats-grid .stats-item,
    #user-stats-container .stats-section-content .stats-item,
    .stats-section-content .stats-grid .stats-item,
    .stats-section-content .stats-item {
        padding: 8px !important;
        min-height: 80px !important;
        font-size: 0.9em !important;
    }
}

/* Ottimizzazione per desktop - assicura 3 colonne quando c'è spazio */
@media (min-width: 769px) {
    #user-stats-container .stats-section-content .stats-grid,
    #user-stats-container .stats-grid,
    .stats-section-content .stats-grid {
        grid-template-columns: 1fr 1fr 1fr !important;
    }
}

/* Stili per il costo totale - nascosto tramite inline style */
.total-cost-row {
    padding: 8px 0;
    border-bottom: none;
    background-color: transparent;
}

/* Usa valori opzionali per il tema */
:root {
    --stats-border-color: #eaeaea;
    --stats-highlight-bg: #fafafa;
    --stats-cost-color: #d35400;
    --stats-bg-color: #ffffff;
    --stats-text-color: #333333;
    --stats-accent-color: #27ae60;
}

/* Stile per la user-info - allineato con subscriber management */
.user-info {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 20px;
    padding: 20px;
    background: #f8f9fa;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    position: relative;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
    background: linear-gradient(135deg, #0073aa 0%, #005177 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    font-size: 18px;
    text-transform: uppercase;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-details {
    flex: 1;
}

.user-name {
    font-weight: 600;
    font-size: 1.1rem;
    color: #333;
    margin-bottom: 4px;
}

.user-role {
    font-size: 0.85rem;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

/* Stili per il pulsante logout nel pannello statistiche del widget */
.user-info .stats-logout-button {
    margin-left: auto;
}

/* Highlight animation per elementi aggiornati */
@keyframes flash-animation {
    0% { background-color: rgba(255, 240, 165, 0); }
    20% { background-color: rgba(255, 240, 165, 0.5); }
    80% { background-color: rgba(255, 240, 165, 0.3); }
    100% { background-color: rgba(255, 240, 165, 0); }
}

/* Stili per il pulsante di logout nel pannello statistiche */
.stats-widget-header {
    padding: 15px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #e1e1e1;
    border-radius: 8px 8px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.stats-user-info {
    display: flex;
    align-items: center;
}

.stats-avatar {
    margin-right: 12px;
}

.stats-user-details {
    flex: 1;
}

.stats-user-name {
    font-weight: 600;
    font-size: 16px;
    color: #2c3e50;
}

.stats-user-role {
    font-size: 13px;
    color: #7f8c8d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-logout-button {
    margin-left: auto;
}

.stats-logout-link {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    border-radius: 6px;
    color: white;
    text-decoration: none;
    font-size: 13px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(231, 76, 60, 0.3);
}

.stats-logout-link:hover {
    background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(231, 76, 60, 0.4);
}

.stats-logout-link .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
    margin-right: 3px;
}

/* Stili per la testata del pannello statistiche con utente e tipo sottoscrizione */
.user-details {
    display: flex;
    align-items: center;
    padding: 10px;
    background: #f9f9f9;
    border-bottom: 1px solid #eaeaea;
    margin-bottom: 10px;
}

#user-avatar {
    margin-right: 10px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #e5e5e5;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #555;
    background-color: #e5e5e5;
}

#user-info {
    flex: 1;
}

#user-name {
    font-weight: bold;
    font-size: 14px;
    color: #333;
    margin-bottom: 2px;
}

#user-subscription-type {
    font-size: 12px;
    color: #666;
    background-color: #f0f0f0;
    padding: 2px 6px;
    border-radius: 3px;
    display: inline-block;
}
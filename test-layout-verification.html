<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Layout Document Viewer</title>
    <link rel="stylesheet" href="assets/css/document-viewer.css">
    <link rel="stylesheet" href="assets/css/document-stats.css">
    <link rel="stylesheet" href="assets/css/document-viewer-override.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .test-header {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }        .column-indicator {
            position: absolute;
            top: 5px;
            right: 10px;
            background: rgba(0,115,170,0.8);
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
        }
        .stats-column { position: relative; }
        .document-form-column { position: relative; }
        .document-display-column { position: relative; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>Test Layout Document Viewer Widget</h1>
            <p>Verifica del layout a tre colonne: <strong>Statistiche (20%)</strong> | <strong>Form (32%)</strong> | <strong>Visualizzazione (40%)</strong></p>
        </div>        <div class="document-viewer-widget">
            <!-- Colonna delle statistiche (sinistra) -->
            <div class="stats-column" id="stats-column">
                <div class="column-indicator">20% - Statistiche</div>
                <h3>Statistiche Utente</h3>

                <div id="user-stats-container" class="stats-section">
                    <div class="stats-section-header">
                        <span>Dati Utilizzo</span>
                        <span class="toggle-icon expanded"></span>
                    </div>
                    <div class="stats-section-content">
                        <div class="user-info">
                            <div class="user-avatar" id="user-avatar"></div>
                            <div class="user-details">
                                <div class="user-name" id="user-name">Test User</div>
                                <div class="user-role" id="user-role">Subscriber</div>
                            </div>
                        </div>

                        <!-- Test layout statistiche a 2 colonne + 1 -->
                        <div class="stats-grid">
                            <div class="stats-row usage-row">
                                <div class="stats-item">
                                    <div class="stats-label">Analisi</div>
                                    <div class="stats-value" id="analyses-count">15</div>
                                </div>
                                <div class="stats-item">
                                    <div class="stats-label">Token</div>
                                    <div class="stats-value" id="tokens-count">45,231</div>
                                </div>
                            </div>

                            <div class="stats-row costs-row">
                                <div class="stats-item cost-item">
                                    <div class="stats-label">Spesa Stimata</div>
                                    <div class="stats-value cost-highlight" id="cost-estimate">€12.45</div>
                                </div>
                                <div class="stats-item cost-item">
                                    <div class="stats-label">Spesa Effettiva</div>
                                    <div class="stats-value cost-highlight" id="actual-cost">€11.87</div>
                                </div>
                            </div>

                            <!-- Costo totale nascosto -->
                            <div class="stats-row total-cost-row">
                                <div class="stats-item total-cost-item">
                                    <div class="stats-label">Spesa Totale</div>
                                    <div class="stats-value total-cost-highlight" id="tot-cost">€128.50</div>
                                </div>
                            </div>

                            <div class="stats-row credit-row">
                                <div class="stats-item credit-item">
                                    <div class="stats-label">Credito Disponibile</div>
                                    <div class="stats-value credit-highlight" id="credits-available">€87.13</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Colonna del form (centro) -->
            <div class="document-form-column">
                <div class="column-indicator">32% - Form</div>
                <h3>Carica e Analizza</h3>

                <form id="document-viewer-form" enctype="multipart/form-data">
                    <!-- Custom logo upload -->
                    <div class="form-row custom-logo-field">
                        <input type="file" id="custom-logo-upload" accept="image/*" />
                        <label for="custom-logo-upload">Scegli Logo</label>
                        <label for="custom-logo-upload" class="logo-description">Logo Personalizzato (opzionale)</label>
                    </div>

                    <!-- Titolo analisi -->
                    <div class="form-row">
                        <label for="analysis-title">Titolo Analisi:</label>
                        <input type="text" id="analysis-title" placeholder="Inserisci un titolo per l'analisi" />
                    </div>

                    <!-- File upload section -->
                    <div class="form-row file-upload-row">
                        <label for="document-upload">Carica Documento (PDF, Word o Immagine):</label>
                        <div class="file-upload-container">
                            <div class="file-upload-input">
                                <input type="file" id="document-upload" accept=".pdf,.doc,.docx" />
                                <label for="document-upload">Scegli PDF/Word</label>

                                <input type="file" id="image-upload" accept="image/*" />
                                <label for="image-upload">Scegli Immagine</label>
                            </div>
                        </div>
                    </div>

                    <!-- Descrizione/domanda -->
                    <div class="form-row">
                        <label for="document-description">Descrizione o Domanda:</label>
                        <textarea id="document-description" rows="4" placeholder="Inserisci una domanda o descrivi cosa vuoi analizzare nel documento"></textarea>
                    </div>

                    <!-- Action buttons -->
                    <div class="form-row">
                        <button type="button" id="analyze-description">Analizza</button>
                        <button type="button" id="clear-document">Cancella</button>
                    </div>
                </form>
            </div>

            <!-- Colonna di visualizzazione (destra) -->
            <div class="document-display-column">
                <div class="column-indicator">40% - Visualizzazione</div>
                <h3>Risultati Analisi</h3>
                <div id="analysis-results">
                    <div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 40px 20px; border-radius: 8px; min-height: 150px; display: flex; align-items: center; justify-content: center;">
                        <div style="text-align: center;">
                            <h5 style="color: #2d3748; font-size: 1.2em; margin-bottom: 10px;">Carica un documento</h5>
                            <p style="color: #666; margin: 0;">per iniziare l'analisi con AI</p>
                        </div>
                    </div>
                </div>

                <h3 style="margin-top: 30px;">Visualizzazione Documento</h3>
                <div style="background: #f9f9f9; padding: 20px; border-radius: 6px; border: 2px dashed #ddd; text-align: center; color: #999;">
                    Area di visualizzazione documento
                </div>
            </div>
        </div>
    </div>
</body>
</html>

/**
 * Document Viewer Override CSS
 * 
 * CSS con specificità MASSIMA per forzare la visibilità della stats column
 * nel Document Viewer Widget in caso di conflitti con altri CSS.
 * 
 * Questo file deve essere caricato DOPO tutti gli altri CSS per garantire
 * la massima priorità.
 */

/* SPECIFICITÀ MASSIMA - Forza la visibilità della stats column */
html body div.document-viewer-widget div.stats-column#stats-column,
html body .document-viewer-widget .stats-column#stats-column,
html body div.document-viewer-widget .stats-column,
html body .document-viewer-widget div.stats-column {
    /* Layout e posizionamento */
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    
    /* Dimensioni - 20% del layout */
    flex: 0 0 20% !important;
    width: 20% !important;
    min-width: 200px !important;
    max-width: 20% !important;
    
    /* Posizionamento e z-index */
    position: relative !important;
    z-index: 999 !important;
    order: 1 !important;
    
    /* Reset margini e padding */
    margin: 0 !important;
    padding: 12px !important;
    box-sizing: border-box !important;
    
    /* Stile visivo */
    background-color: #f9f9f9 !important;
    border: 2px solid #007cba !important; /* Bordo blu per debug */
    border-radius: 6px !important;
    
    /* Previeni conflitti */
    float: none !important;
    clear: none !important;
    transform: none !important;
    
    /* Debug - rimuovere dopo test */
    outline: 3px dashed red !important;
}

/* SPECIFICITÀ MASSIMA - Forza la visibilità di tutti gli elementi figli */
html body div.document-viewer-widget div.stats-column#stats-column *,
html body .document-viewer-widget .stats-column#stats-column *,
html body div.document-viewer-widget .stats-column *,
html body .document-viewer-widget div.stats-column * {
    visibility: visible !important;
    opacity: 1 !important;
    display: inherit !important;
    position: relative !important;
    z-index: 998 !important;
}

/* SPECIFICITÀ MASSIMA - Sezioni delle statistiche */
html body div.document-viewer-widget div.stats-column#stats-column .stats-section,
html body .document-viewer-widget .stats-column#stats-column .stats-section,
html body div.document-viewer-widget .stats-column .stats-section,
html body .document-viewer-widget div.stats-column .stats-section {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 997 !important;
    background-color: #ffffff !important;
    border: 1px solid #e1e1e1 !important;
    border-radius: 8px !important;
    margin-bottom: 20px !important;
}

/* SPECIFICITÀ MASSIMA - Contenuto delle sezioni */
html body div.document-viewer-widget div.stats-column#stats-column .stats-section-content,
html body .document-viewer-widget .stats-column#stats-column .stats-section-content,
html body div.document-viewer-widget .stats-column .stats-section-content,
html body .document-viewer-widget div.stats-column .stats-section-content {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 996 !important;
    padding: 20px !important;
}

/* SPECIFICITÀ MASSIMA - Griglia delle statistiche */
html body div.document-viewer-widget div.stats-column#stats-column .stats-grid,
html body .document-viewer-widget .stats-column#stats-column .stats-grid,
html body div.document-viewer-widget .stats-column .stats-grid,
html body .document-viewer-widget div.stats-column .stats-grid {
    display: grid !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 995 !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 10px !important;
}

/* SPECIFICITÀ MASSIMA - Elementi delle statistiche */
html body div.document-viewer-widget div.stats-column#stats-column .stats-item,
html body .document-viewer-widget .stats-column#stats-column .stats-item,
html body div.document-viewer-widget .stats-column .stats-item,
html body .document-viewer-widget div.stats-column .stats-item {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 994 !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    background: #fff !important;
    border: 1px solid #e1e1e1 !important;
    border-radius: 8px !important;
    padding: 12px !important;
    min-height: 90px !important;
}

/* SPECIFICITÀ MASSIMA - Valori e etichette */
html body div.document-viewer-widget div.stats-column#stats-column .stats-value,
html body .document-viewer-widget .stats-column#stats-column .stats-value,
html body div.document-viewer-widget .stats-column .stats-value,
html body .document-viewer-widget div.stats-column .stats-value,
html body div.document-viewer-widget div.stats-column#stats-column .stats-label,
html body .document-viewer-widget .stats-column#stats-column .stats-label,
html body div.document-viewer-widget .stats-column .stats-label,
html body .document-viewer-widget div.stats-column .stats-label {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 993 !important;
    text-align: center !important;
}

/* SPECIFICITÀ MASSIMA - Titoli */
html body div.document-viewer-widget div.stats-column#stats-column h3,
html body .document-viewer-widget .stats-column#stats-column h3,
html body div.document-viewer-widget .stats-column h3,
html body .document-viewer-widget div.stats-column h3 {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 992 !important;
    margin-top: 0 !important;
    margin-bottom: 15px !important;
    text-align: left !important;
    color: #333 !important;
    font-size: 1.2rem !important;
    font-weight: 600 !important;
}

/* Debug - Messaggio di test per verificare che il CSS sia caricato */
html body div.document-viewer-widget div.stats-column#stats-column::before {
    content: "OVERRIDE CSS ATTIVO" !important;
    position: absolute !important;
    top: -25px !important;
    left: 0 !important;
    background: red !important;
    color: white !important;
    padding: 2px 8px !important;
    font-size: 10px !important;
    font-weight: bold !important;
    border-radius: 3px !important;
    z-index: 1000 !important;
}

/* Media query per schermi medi */
@media (max-width: 1200px) {
    html body div.document-viewer-widget div.stats-column#stats-column,
    html body .document-viewer-widget .stats-column#stats-column,
    html body div.document-viewer-widget .stats-column,
    html body .document-viewer-widget div.stats-column {
        flex: 0 0 15% !important;
        width: 15% !important;
        max-width: 15% !important;
        min-width: 150px !important;
    }
}

/* Media query per schermi piccoli */
@media (max-width: 992px) {
    html body div.document-viewer-widget div.stats-column#stats-column,
    html body .document-viewer-widget .stats-column#stats-column,
    html body div.document-viewer-widget .stats-column,
    html body .document-viewer-widget div.stats-column {
        flex: 1 1 auto !important;
        width: 100% !important;
        max-width: 100% !important;
        min-width: auto !important;
    }
}
